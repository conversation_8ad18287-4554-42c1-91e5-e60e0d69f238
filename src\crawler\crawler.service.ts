import { Injectable, OnModuleInit, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CheerioCrawler, Dataset } from 'crawlee';
import { Cron, CronExpression } from '@nestjs/schedule';

@Injectable()
export class CrawlerService implements OnModuleInit {
  private readonly logger = new Logger(CrawlerService.name);

  constructor(private config: ConfigService) {}

  async onModuleInit() {
    const startUrls = this.config.get<string[]>('CRAWL_START_URLS');
    const concurrent = this.config.get<number>('CRAWL_CONCURRENCY', 5);

    const crawler = new CheerioCrawler({
      requestHandler: async ({ $, request, enqueueLinks }) => {
        this.logger.log(`Crawling ${request.url}`);
        // Extract data
        const title = $('title').text();
        // Save to dataset
        await Dataset.pushData({ url: request.url, title });
        // Enqueue further links
        await enqueueLinks({ selector: 'a.next' });
      },
      maxConcurrency: concurrent,
    });

    await crawler.run(startUrls);
    this.logger.log('Crawl finished');
  }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async handleDailyCrawl() {
    this.logger.log('Daily crawl triggered by cron job');
    await this.onModuleInit();
  }
}
